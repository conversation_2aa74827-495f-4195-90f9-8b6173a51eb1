local assets = {}
local prefabs = {}

-- 性能优化：检查是否有玩家在附近的通用函数
local function HasNearbyPlayer(pos, max_distance)
    max_distance = max_distance or 20
    local max_dist_sq = max_distance * max_distance

    if ThePlayer then
        -- 单机模式
        local dist = ThePlayer:GetDistanceSqToPoint(pos[1], pos[2], pos[3])
        return dist < max_dist_sq
    else
        -- 多人模式
        local players = AllPlayers or {}
        for _, p in ipairs(players) do
            if p and p:IsValid() then
                local dist = p:GetDistanceSqToPoint(pos[1], pos[2], pos[3])
                if dist < max_dist_sq then
                    return true
                end
            end
        end
    end
    return false
end

local function DoSlow(target, mult, dur)
    if target and target.components and target.components.locomotor then
        local tag = "season_blade_slow"
        target.components.locomotor:SetExternalSpeedMultiplier(target, tag, mult)
        target:DoTaskInTime(dur, function()
            if target.components and target.components.locomotor then
                target.components.locomotor:RemoveExternalSpeedMultiplier(target, tag)
            end
        end)
    end
end

local function DoBurnDot(attacker, target, dps, dur)
    if target and target.components and target.components.health and not target.components.health:IsDead() then
        local ticks = math.floor(dur / 0.5)
        for i = 1, ticks do
            target:DoTaskInTime(0.5 * i, function()
                if target.components and target.components.health and not target.components.health:IsDead() then
                    target.components.health:DoDelta(-dps * 0.5, nil, attacker and attacker.prefab or "season_blade")
                end
            end)
        end
    end
end

local function DoWetness(target, amount)
    if target and target.components and target.components.moisture then
        target.components.moisture:DoDelta(amount or 10)
    end
end

-- 客户端预测特效函数
local function PlayClientPredictedFX(inst, attacker, target, season)
    if TheWorld.ismastersim then return end

    -- 错误处理：检查参数有效性
    if not inst or not inst:IsValid() or not attacker or not attacker:IsValid() then
        return
    end

    -- 客户端预测：立即播放轻量级特效
    if attacker.SoundEmitter then
        attacker.SoundEmitter:PlaySound("dontstarve/common/staff_spell", nil, 0.3)
    end

    -- 简单的颜色闪烁效果
    if target and target:IsValid() and target.AnimState then
        local colors = {
            spring = {0.5, 1.0, 0.5, 0.3},
            summer = {1.0, 0.6, 0.2, 0.3},
            autumn = {0.8, 0.5, 0.2, 0.3},
            winter = {0.6, 0.8, 1.0, 0.3}
        }

        local color = colors[season] or colors["autumn"]
        target.AnimState:SetAddColour(color[1], color[2], color[3], color[4])

        -- 0.2秒后恢复
        target:DoTaskInTime(0.2, function()
            if target and target:IsValid() and target.AnimState then
                target.AnimState:SetAddColour(0, 0, 0, 0)
            end
        end)
    end
end

local function BurstFX(attacker, pos, color)
    -- 错误处理：检查参数有效性
    if not attacker or not attacker:IsValid() or not pos then
        print("[SeasonWorkshop] Warning: Invalid parameters for BurstFX")
        return
    end

    -- 客户端/服务端分离：特效只在客户端播放
    if TheWorld.ismastersim then
        -- 服务端：只发送网络事件，不播放特效
        attacker:PushEvent("season_blade_burst_fx", {pos = pos, color = color})
        return
    end

    -- 客户端：播放特效
    local blade_fx_strength = GetModConfigData("blade_fx_strength")
    local vfx_strength = GetModConfigData("vfx_strength") or 1
    local strength = blade_fx_strength ~= nil and blade_fx_strength or vfx_strength

    -- 性能优化：检查是否有玩家在附近，避免无意义的特效
    if not HasNearbyPlayer(pos, 20) then
        return -- 没有玩家在附近，跳过特效
    end

    if attacker.SoundEmitter then
        local vol = strength == 0 and 0.3 or (strength == 2 and 1.0 or 0.6)
        attacker.SoundEmitter:PlaySound("dontstarve/common/staff_spell", nil, vol)
    end

    local fx = SpawnPrefab("staff_castinglight")
    if fx ~= nil then
        fx.Transform:SetPosition(pos[1], pos[2], pos[3])
        -- 根据特效强度调整特效持续时间
        local duration = strength == 0 and 0.2 or (strength == 2 and 0.6 or 0.4)
        fx:DoTaskInTime(duration, function()
            if fx and fx:IsValid() then
                fx:Remove()
            end
        end)
    else
        print("[SeasonWorkshop] Warning: Failed to spawn burst FX")
    end

    if strength > 0 and attacker.components and attacker.components.playercontroller then
        -- 简易相机震动：客户端上由Cameras找API更合适；这里先占位
        -- 可用 TheFocalPoint.components.shakeaura/Camera:Shake 在完整环境中实现
    end
end

local function DoBurst(attacker, target)
    if not attacker or not target then return end
    -- 检查持有者的季节刻印，而不是世界季节
    local season = "autumn" -- 默认值
    if attacker.components and attacker.components.season_engraving then
        season = attacker.components.season_engraving:GetSeason() or "autumn"
    else
        -- 如果没有季节刻印组件，则使用世界季节作为后备
        season = TheWorld.state.season or "autumn"
    end
    local x, y, z = target.Transform:GetWorldPosition()
    local ents
    local isboss = target:HasTag("epic")

    if season == "spring" then
        -- 春季：电链效果，对半径3格内敌人造成伤害并施加湿身
        ents = TheSim:FindEntities(x, y, z, 3, {"_combat"}, {"player", "companion", "FX"})
        local chain_targets = {}

        -- 主目标伤害
        if target.components and target.components.combat then
            local dmg = isboss and TUNING.SEASON_BLADE_BURST_DMG_SPRING_BOSS or TUNING.SEASON_BLADE_BURST_DMG_SPRING
            -- 应用叠层爆发强度配置
            local stack_strength = GetModConfigData("blade_stack_strength") or 1
            local strength_mult = stack_strength == 0 and 0.8 or (stack_strength == 2 and 1.2 or 1.0)
            dmg = dmg * strength_mult
            target.components.combat:GetAttacked(attacker, dmg)
            DoWetness(target, 15)
            table.insert(chain_targets, target)
        end

        -- 电链跳跃（最多2次），跳跃目标伤害减半
        local jump_count = 0
        for _, v in ipairs(ents) do
            if v ~= target and v.components and v.components.combat and jump_count < 2 then
                local base_dmg = v:HasTag("epic") and TUNING.SEASON_BLADE_BURST_DMG_SPRING_BOSS or TUNING.SEASON_BLADE_BURST_DMG_SPRING
                local dmg = base_dmg * 0.5 * strength_mult  -- 跳跃目标伤害减半，应用强度倍率
                v.components.combat:GetAttacked(attacker, dmg)
                DoWetness(v, 15)
                jump_count = jump_count + 1
            end
        end

    elseif season == "summer" then
        -- 夏季：热浪，对半径3格内敌人造成伤害+灼烧
        ents = TheSim:FindEntities(x, y, z, 3, {"_combat"}, {"player", "companion", "FX"})
        -- 应用叠层爆发强度配置
        local stack_strength = GetModConfigData("blade_stack_strength") or 1
        local strength_mult = stack_strength == 0 and 0.8 or (stack_strength == 2 and 1.2 or 1.0)
        for _, v in ipairs(ents) do
            if v.components and v.components.combat then
                local dmg = TUNING.SEASON_BLADE_BURST_DMG_SUMMER * strength_mult
                v.components.combat:GetAttacked(attacker, dmg)
                if not v:HasTag("epic") then
                    DoBurnDot(attacker, v, 5, 3) -- 5点/0.5秒，持续3秒
                end
            end
        end

    elseif season == "autumn" then
        -- 秋季：回复持有者饥饿和理智，对目标造成伤害
        -- 应用叠层爆发强度配置
        local stack_strength = GetModConfigData("blade_stack_strength") or 1
        local strength_mult = stack_strength == 0 and 0.8 or (stack_strength == 2 and 1.2 or 1.0)

        if attacker.components and attacker.components.hunger then
            attacker.components.hunger:DoDelta(2 * strength_mult)
        end
        if attacker.components and attacker.components.sanity then
            attacker.components.sanity:DoDelta(3 * strength_mult)
        end
        if target.components and target.components.combat then
            local dmg = isboss and TUNING.SEASON_BLADE_BURST_DMG_AUTUMN_BOSS or TUNING.SEASON_BLADE_BURST_DMG_AUTUMN
            dmg = dmg * strength_mult
            target.components.combat:GetAttacked(attacker, dmg)
        end

    elseif season == "winter" then
        -- 冬季：对目标和周围敌人施加减速和冰伤
        ents = TheSim:FindEntities(x, y, z, 2, {"_combat"}, {"player", "companion", "FX"})
        -- 应用叠层爆发强度配置
        local stack_strength = GetModConfigData("blade_stack_strength") or 1
        local strength_mult = stack_strength == 0 and 0.8 or (stack_strength == 2 and 1.2 or 1.0)
        -- 将主目标也包含在范围攻击中，避免重复处理
        local processed_targets = {}
        for _, v in ipairs(ents) do
            if v.components and v.components.combat then
                local dmg = TUNING.SEASON_BLADE_BURST_DMG_WINTER * strength_mult
                v.components.combat:GetAttacked(attacker, dmg)
                local mult = v:HasTag("epic") and 0.9 or 0.8  -- Boss减为10%，普通20%
                local dur = v:HasTag("epic") and 1.5 or 2
                DoSlow(v, mult, dur)
                processed_targets[v] = true
            end
        end
        -- 如果主目标不在范围内，单独处理
        if not processed_targets[target] and target.components and target.components.combat then
            local dmg = TUNING.SEASON_BLADE_BURST_DMG_WINTER * strength_mult
            target.components.combat:GetAttacked(attacker, dmg)
            local mult = isboss and 0.9 or 0.8
            local dur = isboss and 1.5 or 2
            DoSlow(target, mult, dur)
        end
    end
    BurstFX(attacker, {x, y, z})
end

local function OnAttack(inst, attacker, target)
    -- 错误处理：检查基本参数
    if not inst or not inst:IsValid() or not attacker or not attacker:IsValid() or not target or not target:IsValid() then
        print("[SeasonWorkshop] Warning: Invalid parameters in OnAttack")
        return
    end

    -- 获取季节信息（客户端和服务端都需要）
    local season = "autumn" -- 默认值
    if attacker.components and attacker.components.season_engraving then
        season = attacker.components.season_engraving:GetSeason() or "autumn"
    else
        -- 如果没有季节刻印组件，则使用世界季节作为后备
        season = TheWorld.state.season or "autumn"
    end

    -- 客户端预测：立即播放特效
    if not TheWorld.ismastersim then
        PlayClientPredictedFX(inst, attacker, target, season)
        return
    end

    -- 服务端逻辑：基础季节效果（每次命中都会尝试应用）

    -- 获取季刃特效强度配置
    local blade_fx_strength = GetModConfigData("blade_fx_strength") or 1
    local fx_mult = blade_fx_strength == 0 and 0.7 or (blade_fx_strength == 2 and 1.3 or 1.0)

    local isboss = target:HasTag("epic")
    if season == "spring" then
        -- 春季：对潮湿目标伤害+25%
        local extra = 0
        if target.GetIsWet ~= nil and target:GetIsWet() then
            extra = (TUNING.SEASON_BLADE_DAMAGE or 34) * 0.25 * fx_mult
        end
        if extra > 0 and target.components and target.components.combat then
            target.components.combat:GetAttacked(attacker, extra)
        end
    elseif season == "summer" then
        -- 夏季：命中时附带轻微灼伤（3点/秒，持续2秒，对Boss改为2点/秒）
        local burn_dmg = fx_mult * (isboss and 2 or 3)
        if not isboss then
            DoBurnDot(attacker, target, burn_dmg, 2)
        else
            -- Boss改为2点/秒
            DoBurnDot(attacker, target, burn_dmg, 2)
        end
    elseif season == "autumn" then
        -- 秋季：命中时15%概率返还1点耐久
        local restore_chance = 0.15 * fx_mult
        if inst.components and inst.components.finiteuses and math.random() < restore_chance then
            inst.components.finiteuses:Use(-1) -- 返还1点耐久
        end
    elseif season == "winter" then
        -- 使用新的seasonal_debuff组件
        if not target.components.seasonal_debuff then
            target:AddComponent("seasonal_debuff")
        end
        local mult = isboss and 0.95 or 0.9
        local dur = (isboss and 1.5 or 2) * fx_mult
        target.components.seasonal_debuff:ApplySlow(mult, dur, false)
    end

    -- 叠层爆发逻辑
    attacker._season_blade = attacker._season_blade or {
        target = nil,
        stacks = 0,
        lasttime = 0,
        cd = 0,
    }
    local data = attacker._season_blade
    local t = GetTime()
    local same = data.target ~= nil and data.target == target
    if not same or (t - data.lasttime) > TUNING.SEASON_BLADE_STACK_WINDOW then
        data.target = target
        data.stacks = 0
    end
    data.stacks = math.min(TUNING.SEASON_BLADE_STACKS_MAX, (data.stacks or 0) + 1)
    data.lasttime = t

    if data.stacks >= TUNING.SEASON_BLADE_STACKS_MAX and t >= (data.cd or 0) then
        -- 触发爆发
        local burst_success = pcall(function()
            DoBurst(attacker, target)
        end)

        if not burst_success then
            print("[SeasonWorkshop] Error: Failed to execute burst attack")
        end

        data.stacks = 0
        data.cd = t + TUNING.SEASON_BLADE_BURST_CD

        -- 若目标是Boss，发送一次“武器爆发”事件用于破盾计数
        -- 但需要检查季节匹配：持有者季节刻印需要与Boss当前阶段匹配
        if target:HasTag("season_warden") and target:IsValid() then
            local holder_season = "autumn" -- 默认值
            if attacker.components and attacker.components.season_engraving then
                holder_season = attacker.components.season_engraving:GetSeason() or "autumn"
            else
                -- 使用世界季节作为后备
                holder_season = TheWorld.state.season or "autumn"
            end

            -- 检查Boss当前阶段是否与持有者季节匹配
            if target._phase == holder_season then
                target:PushEvent("season_sigil", { source = "weapon_burst" })
                print(string.format("[SeasonWorkshop] Weapon burst triggered shield break for %s season", holder_season))
            else
                print(string.format("[SeasonWorkshop] Weapon burst failed - season mismatch: holder=%s, boss=%s", holder_season, target._phase or "unknown"))
            end
        end
    end
end

local function UpdateBladeColor(inst, owner)
    if not owner or not owner.components or not owner.components.season_engraving then
        -- 没有季节刻印，使用默认颜色
        inst.AnimState:SetMultColour(1, 1, 1, 1)
        return
    end

    local season = owner.components.season_engraving:GetSeason() or "autumn"
    -- 根据季节设置武器颜色（统一颜色方案）
    if season == "spring" then
        inst.AnimState:SetMultColour(0.5, 1.0, 0.5, 1.0) -- 春季绿色
    elseif season == "summer" then
        inst.AnimState:SetMultColour(1.0, 0.6, 0.2, 1.0) -- 夏季橙色
    elseif season == "autumn" then
        inst.AnimState:SetMultColour(0.8, 0.5, 0.2, 1.0) -- 秋季褐色
    elseif season == "winter" then
        inst.AnimState:SetMultColour(0.6, 0.8, 1.0, 1.0) -- 冬季蓝色
    end
end

local function onequip(inst, owner)
    owner.AnimState:OverrideSymbol("swap_object", "swap_spear", "swap_spear")
    owner.AnimState:Show("ARM_carry")
    owner.AnimState:Hide("ARM_normal")

    -- 应用季节颜色
    UpdateBladeColor(inst, owner)

    -- 监听季节变化，动态更新颜色
    if owner.components and owner.components.season_engraving then
        inst._season_listener = owner:ListenForEvent("season_engraving_dirty", function()
            UpdateBladeColor(inst, owner)
        end)
    end
end

local function onunequip(inst, owner)
    owner.AnimState:Hide("ARM_carry")
    owner.AnimState:Show("ARM_normal")

    -- 清理季节监听器
    if inst._season_listener then
        owner:RemoveEventCallback("season_engraving_dirty", inst._season_listener)
        inst._season_listener = nil
    end

    -- 清理持有者的叠层数据
    if owner._season_blade then
        owner._season_blade = nil
    end

    -- 恢复默认颜色
    inst.AnimState:SetMultColour(1, 1, 1, 1)
end

-- 武器被移除时的清理
local function OnRemove(inst)
    -- 清理所有监听器
    if inst._season_listener then
        local owner = inst.components and inst.components.inventoryitem and inst.components.inventoryitem.owner
        if owner and owner:IsValid() then
            owner:RemoveEventCallback("season_engraving_dirty", inst._season_listener)
        end
        inst._season_listener = nil
    end

    -- 清理持有者的叠层数据
    local owner = inst.components and inst.components.inventoryitem and inst.components.inventoryitem.owner
    if owner and owner._season_blade then
        owner._season_blade = nil
    end

    print("[SeasonWorkshop] Season blade cleaned up")
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("spear")
    inst.AnimState:SetBuild("spear")
    inst.AnimState:PlayAnimation("idle")

    -- 设置季节图标背景（按设计文档要求：叠加小标记）
    inst.inv_image_bg = {
        atlas = "images/inventoryimages.xml",
        image = "spear.tex"
    }

    inst:AddTag("sharp")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst.components.inspectable.getstatus = function(inst)
        -- 检查持有者的季节刻印
        local season = "autumn" -- 默认值
        local holder = nil
        if inst.components and inst.components.inventoryitem then
            holder = inst.components.inventoryitem.owner
        end

        if holder and holder.components and holder.components.season_engraving then
            season = holder.components.season_engraving:GetSeason() or "autumn"
        else
            -- 如果没有持有者或季节刻印，使用世界季节
            season = TheWorld.state.season or "autumn"
        end

        local season_names = {
            spring = "春季",
            summer = "夏季",
            autumn = "秋季",
            winter = "冬季"
        }
        local effects = {
            spring = "对潮湿目标伤害+25%，爆发：电链攻击",
            summer = "命中附带灼伤，爆发：热浪攻击",
            autumn = "15%概率返还耐久，爆发：回复饥饿理智",
            winter = "命中附带减速，爆发：冰霜攻击"
        }

        local source = holder and holder.components and holder.components.season_engraving and "刻印" or "世界"
        return source .. season_names[season] .. "：" .. effects[season]
    end

    inst:AddComponent("inventoryitem")

    inst:AddComponent("weapon")
    inst.components.weapon:SetDamage(TUNING.SEASON_BLADE_DAMAGE)
    inst.components.weapon:SetOnAttack(OnAttack)

    inst:AddComponent("finiteuses")
    inst.components.finiteuses:SetMaxUses(TUNING.SEASON_BLADE_USES)
    inst.components.finiteuses:SetUses(TUNING.SEASON_BLADE_USES)
    inst.components.finiteuses:SetOnFinished(inst.Remove)

    inst:AddComponent("equippable")
    inst.components.equippable:SetOnEquip(onequip)
    inst.components.equippable:SetOnUnequip(onunequip)

    -- 添加移除监听
    inst:ListenForEvent("onremove", OnRemove)

    return inst
end

return Prefab("season_blade", fn, assets, prefabs)
