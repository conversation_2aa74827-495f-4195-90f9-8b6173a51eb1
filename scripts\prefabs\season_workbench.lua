local assets = {}
local prefabs = {}

-- 引入网络变量
local net_bool = GLOBAL.net_bool

local function onbuilt(inst)
    if inst.SoundEmitter then
        inst.SoundEmitter:PlaySound("dontstarve/common/craftable/alchemy_engine")
    end
end

local function ApplySeasonalStyling(inst)
    -- 按设计文档要求：复用alchemist改色+微发光，静态结构
    -- 设置固定的季节主题改色（四季融合色调）
    inst.AnimState:SetMultColour(0.9, 0.8, 0.7, 1.0) -- 暖色调，体现四季工坊主题
end

local function AddGlowEffect(inst)
    -- 添加微发光效果（仅在服务端）
    if TheWorld.ismastersim then
        inst:AddComponent("lighttweener")
        inst:AddComponent("light")
        inst.components.light:SetFalloff(0.7)
        inst.components.light:SetIntensity(0.5)
        inst.components.light:SetRadius(2)
        inst.components.light:SetColour(0.9, 0.8, 0.7) -- 与改色一致的暖色光
        inst.components.light:Enable(true)

        -- 同步发光状态到客户端
        if inst._net_glowing then
            inst._net_glowing:set(true)
        end
    end
end

local function fn()
    local inst = CreateEntity()
    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddNetwork()

    -- 基于炼金引擎（alchemist）贴图，沿用原有动画
    inst.AnimState:SetBank("researchlab2")
    inst.AnimState:SetBuild("researchlab2")
    inst.AnimState:PlayAnimation("idle")

    -- 应用季节主题样式（固定改色）
    ApplySeasonalStyling(inst)

    -- 添加建筑标签
    inst:AddTag("structure")
    inst:AddTag("prototyper")
    inst:AddTag("seasonal")

    -- 小地图图标
    inst.entity:AddMiniMapEntity()
    inst.MiniMapEntity:SetIcon("researchlab2.png")
    inst.MiniMapEntity:SetPriority(5)

    -- 网络变量初始化
    inst._net_glowing = net_bool(inst.GUID, "season_workbench.glowing", "workbench_glow_dirty")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        -- 客户端监听发光状态
        inst:ListenForEvent("workbench_glow_dirty", function()
            if inst and inst:IsValid() then
                local is_glowing = inst._net_glowing:value()
                if is_glowing then
                    -- 客户端视觉发光效果
                    if inst.AnimState then
                        inst.AnimState:SetBloomEffectHandle("shaders/anim.ksh")
                    end
                end
            end
        end)
        return inst
    end

    inst:AddComponent("inspectable")
    inst.components.inspectable.nameoverride = "SEASON_WORKBENCH"

    inst:AddComponent("prototyper")
    inst.components.prototyper.trees = { SEASONAL = 1 }

    inst:AddComponent("lootdropper")
    -- 设置掉落物品（建筑被破坏时掉落部分材料）
    inst.components.lootdropper:SetLoot({"boards", "cutstone", "gears"})

    -- 添加可工作组件（可被锤子等工具破坏）
    inst:AddComponent("workable")
    inst.components.workable:SetWorkAction(ACTIONS.HAMMER)
    inst.components.workable:SetWorkLeft(4)
    inst.components.workable:SetOnFinishCallback(function(inst, worker)
        if inst.components.lootdropper then
            inst.components.lootdropper:DropLoot()
        end
        inst:Remove()
    end)

    -- 按设计文档要求：静态结构，季芯合成通过正常配方系统实现

    -- 添加微发光效果
    AddGlowEffect(inst)

    -- 数据持久化
    inst.OnSave = function(inst, data)
        data.glowing = inst._glowing
    end

    inst.OnLoad = function(inst, data)
        if data and data.glowing ~= nil then
            inst._glowing = data.glowing
            if inst._net_glowing then
                inst._net_glowing:set(inst._glowing)
            end
            -- 恢复发光效果
            if inst._glowing then
                AddGlowEffect(inst)
            end
        end
    end

    inst:ListenForEvent("onbuilt", onbuilt)

    return inst
end

return Prefab("season_workbench", fn, assets, prefabs),
       MakePlacer("season_workbench_placer", "researchlab2", "researchlab2", "idle")
