-- 引入网络变量
local net_string = GLOBAL.net_string

local function MakeSigilField(name)
    local assets = {}
    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddNetwork()

        inst:AddTag("FX")
        inst.persists = false

        -- 网络同步季节属性
        inst._net_season = net_string(inst.GUID, "sigil_field.season", "season_dirty")

        inst.entity:SetPristine()
        if not TheWorld.ismastersim then
            -- 客户端监听季节变化
            inst:ListenForEvent("season_dirty", function()
                inst._season = inst._net_season:value()
            end)
            return inst
        end

        -- 服务端初始化
        inst._net_season:set("")

        local function pulse()
            if not inst._season then return end
            local x, y, z = inst.Transform:GetWorldPosition()
            local rad = 3
            local ents = TheSim:FindEntities(x, y, z, rad, {"season_warden"})
            for _, v in ipairs(ents) do
                v:PushEvent("season_sigil", {season = inst._season})
            end

            -- 性能优化：检查是否有玩家在附近再播放特效
            local nearby_player = false
            local players = AllPlayers or {}
            for _, p in ipairs(players) do
                if p and p:IsValid() then
                    local dist = p:GetDistanceSqToPoint(x, 0, z)
                    if dist < 400 then -- 20格距离内
                        nearby_player = true
                        break
                    end
                end
            end

            if nearby_player then
                local fx = SpawnPrefab("staff_castinglight")
                if fx then
                    fx.Transform:SetPosition(x, 0, z)
                    fx:DoTaskInTime(0.4, fx.Remove)
                end
            end
        end

        for i=0,4 do
            inst:DoTaskInTime(0.4 * i, pulse)
        end
        inst:DoTaskInTime(2.2, inst.Remove)

        return inst
    end
    return Prefab(name, fn, assets)
end

local function MakeSigil(season, bankbuild)
    local name = "season_sigil_"..season
    local assets = {}
    local function ondeploy(inst, pt, deployer)
        local field = SpawnPrefab("sigil_field")
        if field then
            field._season = season
            -- 同步季节属性到网络
            if TheWorld.ismastersim and field._net_season then
                field._net_season:set(season)
            end
            field.Transform:SetPosition(pt.x, 0, pt.z)
        end
        if deployer and deployer.SoundEmitter then
            deployer.SoundEmitter:PlaySound("dontstarve/common/staff_spell")
        end
        inst:Remove()
    end

    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddAnimState()
        inst.entity:AddNetwork()

        MakeInventoryPhysics(inst)

        inst.AnimState:SetBank("trinket_6")
        inst.AnimState:SetBuild("trinket_6")
        inst.AnimState:PlayAnimation("idle")

        -- 根据季节设置颜色（按设计文档：统一复用trinket_6并改色）
        if season == "spring" then
            inst.AnimState:SetMultColour(0.5, 1.0, 0.5, 1.0) -- 绿色
        elseif season == "summer" then
            inst.AnimState:SetMultColour(1.0, 0.6, 0.2, 1.0) -- 橙色
        elseif season == "autumn" then
            inst.AnimState:SetMultColour(0.8, 0.5, 0.2, 1.0) -- 褐色
        elseif season == "winter" then
            inst.AnimState:SetMultColour(0.6, 0.8, 1.0, 1.0) -- 蓝色
        end

        inst:AddTag("season_sigil")
        inst.inv_image_bg = { atlas = nil, image = nil }

        inst.entity:SetPristine()
        if not TheWorld.ismastersim then
            return inst
        end

        inst._season = season  -- 保存季节属性

        inst:AddComponent("inspectable")
        inst.components.inspectable.getstatus = function(inst)
            local season_names = {
                spring = "春季",
                summer = "夏季",
                autumn = "秋季",
                winter = "冬季"
            }
            if inst._season then
                return string.format("%s破盾符文，可瓦解Boss的%s护盾", season_names[inst._season], season_names[inst._season])
            else
                return "季节破盾符文"
            end
        end

        inst:AddComponent("inventoryitem")
        inst:AddComponent("deployable")
        inst.components.deployable.ondeploy = ondeploy
        inst.components.deployable:SetDeployMode(DEPLOYMODE.DEFAULT)
        inst.components.deployable:SetQuickDeploy(true)

        return inst
    end

    return Prefab(name, fn, assets)
end

local sigil_field = MakeSigilField("sigil_field")
-- 按设计文档要求：统一复用trinket_6 build并改色，不使用不同的build
local sigil_spring = MakeSigil("spring", "trinket_6")
local sigil_summer = MakeSigil("summer", "trinket_6")
local sigil_autumn = MakeSigil("autumn", "trinket_6")
local sigil_winter = MakeSigil("winter", "trinket_6")

return sigil_field, sigil_spring, sigil_summer, sigil_autumn, sigil_winter
