-- 网络同步验证工具
-- 用于验证DST多人联机的网络同步规范

local _G = GLOBAL
local TheWorld = _G.TheWorld
local TheNet = _G.TheNet

local NetworkValidator = {}

-- 验证网络变量是否正确挂载
function NetworkValidator.ValidateNetworkEntity(entity)
    if not entity then
        return false, "Entity is nil"
    end

    if not entity:IsValid() then
        return false, "Entity is not valid"
    end

    if not entity.GUID then
        return false, "Entity has no GUID"
    end

    if not entity.entity then
        return false, "Entity has no entity component"
    end

    -- 检查是否为正确的网络实体
    local is_forest_network = entity == TheWorld.net
    local is_cave_network = entity.prefab == "cave_network"
    local is_player = entity:HasTag("player")
    local has_network_component = entity.Network ~= nil

    if not (is_forest_network or is_cave_network or is_player or has_network_component) then
        return false, "Entity is not a valid network entity"
    end

    return true, "Valid network entity"
end

-- 验证网络变量命名规范
function NetworkValidator.ValidateNetworkVariableName(name)
    if not name or type(name) ~= "string" then
        return false, "Name must be a string"
    end

    if name == "" then
        return false, "Name cannot be empty"
    end

    if #name > 64 then
        return false, "Name too long (max 64 characters)"
    end

    -- 检查是否包含非法字符
    if name:match("[^%w%._%-]") then
        return false, "Name contains invalid characters (only alphanumeric, dot, underscore, hyphen allowed)"
    end

    return true, "Valid network variable name"
end

-- 验证网络同步状态
function NetworkValidator.ValidateNetworkSync()
    local issues = {}
    local warnings = {}

    -- 检查基本网络状态
    if not TheWorld then
        table.insert(issues, "TheWorld not available")
        return false, issues, warnings
    end

    if not TheNet then
        table.insert(issues, "TheNet not available")
        return false, issues, warnings
    end

    -- 检查服务端/客户端状态一致性
    local is_master_sim = TheWorld.ismastersim
    local is_server = TheNet:GetIsServer()
    local is_client = TheNet:GetIsClient()
    local is_dedicated = TheNet:IsDedicated()

    if is_master_sim ~= is_server then
        table.insert(issues, "Inconsistent server state: ismastersim=" .. tostring(is_master_sim) .. ", GetIsServer=" .. tostring(is_server))
    end

    if is_client and is_server and not is_dedicated then
        -- 这是正常的客户端主机模式
        table.insert(warnings, "Client host mode detected")
    end

    -- 检查forest_network可用性
    if not TheWorld.net then
        table.insert(issues, "forest_network not available")
    else
        local valid, msg = NetworkValidator.ValidateNetworkEntity(TheWorld.net)
        if not valid then
            table.insert(issues, "forest_network validation failed: " .. msg)
        end
    end

    return #issues == 0, issues, warnings
end

-- 验证组件的网络同步设置
function NetworkValidator.ValidateComponentNetworkSync(component, expected_vars)
    if not component then
        return false, {"Component is nil"}
    end

    local issues = {}
    local warnings = {}

    -- 检查网络变量是否存在
    if expected_vars then
        for var_name, var_info in pairs(expected_vars) do
            local net_var = component[var_name]
            if not net_var then
                table.insert(issues, "Missing network variable: " .. var_name)
            else
                -- 检查网络变量类型
                if var_info.type then
                    local expected_type = var_info.type
                    -- 这里可以添加更详细的类型检查
                end
            end
        end
    end

    -- 检查组件是否在正确的实体上
    if component.inst then
        local valid, msg = NetworkValidator.ValidateNetworkEntity(component.inst)
        if not valid then
            table.insert(warnings, "Component attached to invalid network entity: " .. msg)
        end
    end

    return #issues == 0, issues, warnings
end

-- 生成网络同步报告
function NetworkValidator.GenerateNetworkReport()
    local report = {
        timestamp = os.time(),
        world_state = {},
        network_state = {},
        components = {},
        issues = {},
        warnings = {}
    }

    -- 收集世界状态信息
    if TheWorld then
        report.world_state = {
            is_master_sim = TheWorld.ismastersim,
            season = TheWorld.state and TheWorld.state.season or "unknown",
            day = TheWorld.state and TheWorld.state.cycles or 0,
            phase = TheWorld.state and TheWorld.state.phase or "unknown"
        }
    end

    -- 收集网络状态信息
    if TheNet then
        report.network_state = {
            is_server = TheNet:GetIsServer(),
            is_client = TheNet:GetIsClient(),
            is_dedicated = TheNet:IsDedicated(),
            player_count = #(_G.AllPlayers or {})
        }
    end

    -- 验证整体网络同步
    local sync_valid, sync_issues, sync_warnings = NetworkValidator.ValidateNetworkSync()
    for _, issue in ipairs(sync_issues) do
        table.insert(report.issues, issue)
    end
    for _, warning in ipairs(sync_warnings) do
        table.insert(report.warnings, warning)
    end

    -- 检查Season Workshop特定组件
    if TheWorld and TheWorld.net then
        local forest_network = TheWorld.net
        
        -- 检查季风乱流管理器
        if forest_network.components and forest_network.components.seasonal_gust_manager then
            local gust_manager = forest_network.components.seasonal_gust_manager
            local expected_vars = {
                _net_active = {type = "bool"},
                _net_season = {type = "string"},
                _net_remaining = {type = "float"},
                _net_events_left = {type = "tinybyte"},
                _net_next_event_time = {type = "float"}
            }
            
            local valid, issues, warnings = NetworkValidator.ValidateComponentNetworkSync(gust_manager, expected_vars)
            report.components.seasonal_gust_manager = {
                valid = valid,
                issues = issues,
                warnings = warnings
            }
        end

        -- 检查入侵管理器
        if forest_network.components and forest_network.components.season_warden_invasion then
            report.components.season_warden_invasion = {
                valid = true,
                issues = {},
                warnings = {}
            }
        end

        -- 检查网络同步管理器
        if forest_network.components and forest_network.components.network_sync_manager then
            report.components.network_sync_manager = {
                valid = true,
                issues = {},
                warnings = {}
            }
        end
    end

    return report
end

-- 打印网络同步报告
function NetworkValidator.PrintNetworkReport()
    local report = NetworkValidator.GenerateNetworkReport()
    
    print("=== Season Workshop 网络同步报告 ===")
    print("时间戳: " .. (report.timestamp or "unknown"))
    
    print("\n世界状态:")
    print("  主服务器: " .. tostring(report.world_state.is_master_sim))
    print("  季节: " .. (report.world_state.season or "unknown"))
    print("  天数: " .. (report.world_state.day or 0))
    print("  阶段: " .. (report.world_state.phase or "unknown"))
    
    print("\n网络状态:")
    print("  服务器: " .. tostring(report.network_state.is_server))
    print("  客户端: " .. tostring(report.network_state.is_client))
    print("  专用服务器: " .. tostring(report.network_state.is_dedicated))
    print("  玩家数量: " .. (report.network_state.player_count or 0))
    
    if #report.issues > 0 then
        print("\n发现问题:")
        for _, issue in ipairs(report.issues) do
            print("  ❌ " .. issue)
        end
    end
    
    if #report.warnings > 0 then
        print("\n警告:")
        for _, warning in ipairs(report.warnings) do
            print("  ⚠️ " .. warning)
        end
    end
    
    print("\n组件状态:")
    for comp_name, comp_info in pairs(report.components) do
        local status = comp_info.valid and "✅" or "❌"
        print("  " .. status .. " " .. comp_name)
        
        if #comp_info.issues > 0 then
            for _, issue in ipairs(comp_info.issues) do
                print("    ❌ " .. issue)
            end
        end
        
        if #comp_info.warnings > 0 then
            for _, warning in ipairs(comp_info.warnings) do
                print("    ⚠️ " .. warning)
            end
        end
    end
    
    if #report.issues == 0 and #report.warnings == 0 then
        print("\n✅ 网络同步状态良好")
    end
    
    return report
end

return NetworkValidator
