-- 错误处理和恢复系统
-- 提供统一的错误处理、日志记录和恢复机制

local _G = GLOBAL
local TheWorld = _G.TheWorld

local ErrorHandler = {}

-- 错误级别
ErrorHandler.LEVELS = {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3,
    CRITICAL = 4
}

-- 错误统计
ErrorHandler.stats = {
    total_errors = 0,
    errors_by_level = {},
    errors_by_component = {},
    last_error_time = 0
}

-- 初始化错误统计
for level_name, level_value in pairs(ErrorHandler.LEVELS) do
    ErrorHandler.stats.errors_by_level[level_name] = 0
end

-- 日志函数
function ErrorHandler.Log(level, component, message, data)
    -- 参数验证
    if not level or not component or not message then
        print("[SeasonWorkshop] ErrorHandler: Invalid log parameters")
        return
    end
    
    -- 获取级别名称
    local level_name = "UNKNOWN"
    for name, value in pairs(ErrorHandler.LEVELS) do
        if value == level then
            level_name = name
            break
        end
    end
    
    -- 更新统计
    ErrorHandler.stats.total_errors = ErrorHandler.stats.total_errors + 1
    ErrorHandler.stats.errors_by_level[level_name] = (ErrorHandler.stats.errors_by_level[level_name] or 0) + 1
    ErrorHandler.stats.errors_by_component[component] = (ErrorHandler.stats.errors_by_component[component] or 0) + 1
    ErrorHandler.stats.last_error_time = GetTime() or 0
    
    -- 格式化消息
    local timestamp = GetTime() and string.format("%.1f", GetTime()) or "0.0"
    local formatted_message = string.format("[%s][%s][%s] %s", timestamp, level_name, component, message)
    
    -- 添加数据信息
    if data then
        formatted_message = formatted_message .. " | Data: " .. tostring(data)
    end
    
    -- 输出日志
    print(formatted_message)
    
    -- 严重错误处理
    if level >= ErrorHandler.LEVELS.CRITICAL then
        ErrorHandler.HandleCriticalError(component, message, data)
    end
end

-- 便捷日志函数
function ErrorHandler.Debug(component, message, data)
    ErrorHandler.Log(ErrorHandler.LEVELS.DEBUG, component, message, data)
end

function ErrorHandler.Info(component, message, data)
    ErrorHandler.Log(ErrorHandler.LEVELS.INFO, component, message, data)
end

function ErrorHandler.Warning(component, message, data)
    ErrorHandler.Log(ErrorHandler.LEVELS.WARNING, component, message, data)
end

function ErrorHandler.Error(component, message, data)
    ErrorHandler.Log(ErrorHandler.LEVELS.ERROR, component, message, data)
end

function ErrorHandler.Critical(component, message, data)
    ErrorHandler.Log(ErrorHandler.LEVELS.CRITICAL, component, message, data)
end

-- 安全执行函数（带错误捕获）
function ErrorHandler.SafeCall(component, func, error_message, ...)
    if not func or type(func) ~= "function" then
        ErrorHandler.Error(component, "SafeCall: Invalid function provided")
        return false, nil
    end
    
    local success, result = pcall(func, ...)
    
    if not success then
        local err_msg = error_message or "Function execution failed"
        ErrorHandler.Error(component, err_msg .. " | Error: " .. tostring(result))
        return false, result
    end
    
    return true, result
end

-- 实例有效性检查
function ErrorHandler.ValidateInstance(component, inst, instance_name)
    instance_name = instance_name or "instance"
    
    if not inst then
        ErrorHandler.Error(component, instance_name .. " is nil")
        return false
    end
    
    if not inst.IsValid or not inst:IsValid() then
        ErrorHandler.Error(component, instance_name .. " is invalid")
        return false
    end
    
    return true
end

-- 参数验证
function ErrorHandler.ValidateParams(component, params, required_params)
    if not params then
        ErrorHandler.Error(component, "Parameters table is nil")
        return false
    end
    
    if not required_params then
        return true -- 没有必需参数
    end
    
    for _, param_name in ipairs(required_params) do
        if params[param_name] == nil then
            ErrorHandler.Error(component, "Required parameter missing: " .. param_name)
            return false
        end
    end
    
    return true
end

-- 网络变量验证
function ErrorHandler.ValidateNetVar(component, net_var, var_name)
    var_name = var_name or "network_variable"
    
    if not net_var then
        ErrorHandler.Error(component, var_name .. " is nil")
        return false
    end
    
    -- 尝试访问网络变量
    local success, value = pcall(function()
        return net_var:value()
    end)
    
    if not success then
        ErrorHandler.Error(component, var_name .. " access failed: " .. tostring(value))
        return false
    end
    
    return true, value
end

-- 处理严重错误
function ErrorHandler.HandleCriticalError(component, message, data)
    ErrorHandler.Info("ErrorHandler", "Handling critical error from " .. component)
    
    -- 尝试通知所有玩家
    if TheWorld and TheWorld.ismastersim then
        local AllPlayers = _G.AllPlayers or {}
        for _, player in ipairs(AllPlayers) do
            if player and player:IsValid() and player.components and player.components.talker then
                player.components.talker:Say("系统错误：" .. component .. " 组件异常", 5)
            end
        end
    end
    
    -- 记录详细错误信息
    ErrorHandler.Info("ErrorHandler", "Critical error details - Component: " .. component .. ", Message: " .. message)
    if data then
        ErrorHandler.Info("ErrorHandler", "Error data: " .. tostring(data))
    end
end

-- 获取错误统计
function ErrorHandler.GetStats()
    return {
        total_errors = ErrorHandler.stats.total_errors,
        errors_by_level = ErrorHandler.stats.errors_by_level,
        errors_by_component = ErrorHandler.stats.errors_by_component,
        last_error_time = ErrorHandler.stats.last_error_time,
        uptime = GetTime() or 0
    }
end

-- 重置错误统计
function ErrorHandler.ResetStats()
    ErrorHandler.stats.total_errors = 0
    ErrorHandler.stats.errors_by_level = {}
    ErrorHandler.stats.errors_by_component = {}
    ErrorHandler.stats.last_error_time = 0
    
    -- 重新初始化级别统计
    for level_name, level_value in pairs(ErrorHandler.LEVELS) do
        ErrorHandler.stats.errors_by_level[level_name] = 0
    end
    
    ErrorHandler.Info("ErrorHandler", "Error statistics reset")
end

-- 检查系统健康状态
function ErrorHandler.CheckSystemHealth()
    local stats = ErrorHandler.GetStats()
    local current_time = GetTime() or 0
    
    local health_status = {
        overall = "HEALTHY",
        issues = {},
        recommendations = {}
    }
    
    -- 检查错误率
    if stats.total_errors > 100 then
        health_status.overall = "DEGRADED"
        table.insert(health_status.issues, "High error count: " .. stats.total_errors)
        table.insert(health_status.recommendations, "Consider restarting the mod or checking for conflicts")
    end
    
    -- 检查严重错误
    if stats.errors_by_level.CRITICAL > 0 then
        health_status.overall = "CRITICAL"
        table.insert(health_status.issues, "Critical errors detected: " .. stats.errors_by_level.CRITICAL)
        table.insert(health_status.recommendations, "Immediate attention required")
    end
    
    -- 检查最近错误频率
    if current_time - stats.last_error_time < 10 and stats.total_errors > 10 then
        health_status.overall = "UNSTABLE"
        table.insert(health_status.issues, "Recent high error activity")
        table.insert(health_status.recommendations, "Monitor system closely")
    end
    
    return health_status
end

-- 自动恢复机制
function ErrorHandler.PerformAutoRecovery()
    local stats = ErrorHandler.GetStats()
    local current_time = GetTime() or 0

    -- 检查是否需要自动恢复
    local need_recovery = false
    local recovery_reason = ""

    -- 检查错误率
    if stats.total_errors > 50 and (current_time - stats.last_error_time) < 60 then
        need_recovery = true
        recovery_reason = "High error rate detected"
    end

    -- 检查严重错误
    if stats.errors_by_level.CRITICAL > 0 then
        need_recovery = true
        recovery_reason = "Critical errors detected"
    end

    if need_recovery then
        ErrorHandler.Info("ErrorHandler", "Auto recovery triggered: " .. recovery_reason)
        ErrorHandler.PerformSystemRecovery()
        return true
    end

    return false
end

-- 组件健康检查
function ErrorHandler.CheckComponentHealth(component_name, instance)
    local health_issues = {}

    -- 检查实例有效性
    if not instance or not instance.IsValid or not instance:IsValid() then
        table.insert(health_issues, "Instance invalid")
    end

    -- 检查网络变量（如果存在）
    if instance and instance._net_vars then
        for var_name, net_var in pairs(instance._net_vars) do
            if not net_var or not net_var.value then
                table.insert(health_issues, "Network variable corrupted: " .. var_name)
            end
        end
    end

    -- 检查组件特定的健康状态
    if instance and instance.PerformSelfCheck then
        local component_healthy = instance:PerformSelfCheck()
        if not component_healthy then
            table.insert(health_issues, "Component self-check failed")
        end
    end

    return #health_issues == 0, health_issues
end

-- 执行系统恢复
function ErrorHandler.PerformSystemRecovery()
    ErrorHandler.Info("ErrorHandler", "Starting system recovery...")

    -- 重置错误统计
    ErrorHandler.ResetStats()

    -- 尝试恢复网络同步管理器
    if TheWorld and TheWorld.components and TheWorld.components.network_sync_manager then
        local sync_manager = TheWorld.components.network_sync_manager
        if sync_manager.PerformErrorRecovery then
            sync_manager:PerformErrorRecovery()
        end
    end

    -- 执行性能清理
    local PerformanceMonitor = _G.SeasonWorkshopPerformanceMonitor
    if PerformanceMonitor and PerformanceMonitor.PerformCleanup then
        PerformanceMonitor.PerformCleanup()
    end

    -- 通知管理员
    if TheWorld and TheWorld.ismastersim then
        local AllPlayers = _G.AllPlayers or {}
        for _, player in ipairs(AllPlayers) do
            if player and player:IsValid() and player.components and player.components.talker then
                player.components.talker:Say("系统恢复完成", 3)
            end
        end
    end

    ErrorHandler.Info("ErrorHandler", "System recovery completed")
end

-- 定期健康检查
function ErrorHandler.PerformPeriodicCheck()
    local current_time = GetTime() or 0

    -- 避免过于频繁的检查
    if ErrorHandler.last_periodic_check and (current_time - ErrorHandler.last_periodic_check) < 120 then
        return
    end

    ErrorHandler.last_periodic_check = current_time

    -- 检查系统健康状态
    local health = ErrorHandler.CheckSystemHealth()

    -- 如果系统不健康，尝试自动恢复
    if health.overall ~= "HEALTHY" then
        ErrorHandler.Warning("ErrorHandler", "System health degraded: " .. health.overall)

        -- 尝试自动恢复
        local recovered = ErrorHandler.PerformAutoRecovery()
        if recovered then
            ErrorHandler.Info("ErrorHandler", "Auto recovery completed")
        end
    end

    -- 执行性能检查
    local PerformanceMonitor = _G.SeasonWorkshopPerformanceMonitor
    if PerformanceMonitor and PerformanceMonitor.PerformAutoCheck then
        PerformanceMonitor.PerformAutoCheck()
    end
end

-- 导出全局访问函数
_G.SeasonWorkshopErrorHandler = ErrorHandler

return ErrorHandler
